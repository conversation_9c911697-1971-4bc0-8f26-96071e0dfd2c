package middleware

import (
	"digital-transformation-api/infrastructure"
	"digital-transformation-api/libs/apps"
	"digital-transformation-api/libs/errs"
	"net/http"

	"github.com/gin-gonic/gin"
)

// ApiKey middleware validates API key from headers, query parameters, or request body
func ApiKey() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		expectedApiKey := infrastructure.GetApiKey()
		providedApiKey := ctx.GetHeader(apps.ApiKey)
		if expectedApiKey == "" {
			// If no API key is configured, skip validation
			ctx.Next()
			return
		}

		// Validate API key presence
		if providedApiKey == "" {
			err := errs.NewCustom(
				http.StatusUnauthorized,
				errs.Err40100,
				"API key is required",
				"API key must be provided in x-api-key header, Authorization header, or as a query parameter",
			)
			ctx.AbortWithStatusJSON(err.Status(), err)
			return
		}

		// Validate API key value
		if providedApiKey != expectedApiKey {
			err := errs.NewCustom(
				http.StatusUnauthorized,
				errs.Err40101,
				"Invalid API key",
				"The provided API key is not valid",
			)
			ctx.AbortWithStatusJSON(err.Status(), err)
			return
		}

		ctx.Next()
	}
}
