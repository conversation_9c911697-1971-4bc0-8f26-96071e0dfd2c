package apps

import (
	"digital-transformation-api/libs/errs"
	"net/http"
)

const (
	TraceID = "traceId"
	SpanID  = "spanId"

	RouteContext = "rctx"
)

const (
	StartInbound = "START INBOUND | %s | %s | %s"
	EndInbound   = "END INBOUND | %v | %v | %s | %s"

	StartOutbound = "START OUTBOUND | %s | %s"
	EndOutbound   = "END OUTBOUND | %d | %s | %s"
)

const (
	Header = "header"
	Body   = "body"
)

var HeaderMaskingList = map[string]bool{
	"Authorization": true,
}

var ErrorRouteContextNotFound = errs.NewCustom(http.StatusInternalServerError, "50000", "route context not found", "")

// Custom application headers
const (
	LineToken = "line-token"
	ApiKey    = "x-api-key"
)


