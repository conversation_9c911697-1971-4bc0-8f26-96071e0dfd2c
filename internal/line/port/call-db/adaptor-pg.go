package calldb

import (
	"digital-transformation-api/internal/common/domain"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gorms"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/logs"
	"digital-transformation-api/libs/responses"
	"errors"

	"gorm.io/gorm"
)

type adaptorPG struct {
	db *gorm.DB
}

func NewAdaptorPG(db *gorm.DB) Port {
	return &adaptorPG{db: db}
}

func (a *adaptorPG) GetStaffInfo(request *GetStaffInfoRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	lineToken := request.LineToken

	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var staff domain.Staff
	if err := tx.Table("staff").Where("line_token = ?", lineToken).Take(&staff).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errs.NewCustom(404, errs.Err50004, "Staff not found", "Staff not found")
		}
		l.Errorf("query staff by line_token error: %v", err)
		return nil, errs.NewInternalError()
	}

	return responses.NewServiceSuccessResponse(staff), nil
}

func (a *adaptorPG) RegisterStaff(request *RegisterStaffRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error) {
	l = logs.NewSpanLog(l)

	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	// Check if staff exists with the provided employee_id and phone
	var staff domain.Staff
	if err := tx.Table("staff").Where("line_token = ?", request.LineToken).Take(&staff).Error; err == nil {
		if staff.EmployeeID != nil {
			return nil, errs.NewCustom(500, errs.Err50005, "เข้าสู่ระบบไม่สำเร็จ", "Staff is already registered with a LINE token")
		}
	}

	if err := tx.Table("staff").Where("employee_id = ? AND phone = ?", request.EmployeeID, request.Phone).Take(&staff).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errs.NewCustom(500, errs.Err50005, "เข้าสู่ระบบไม่สำเร็จ", "Staff not found with provided employee ID and phone")
		}
		l.Errorf("query staff by employee_id and phone error: %v", err)
		return nil, errs.NewInternalError()
	}

	// Check if staff is already linked to a line_token
	if staff.LineToken != nil && *staff.LineToken != "" {
		return nil, errs.NewCustom(500, errs.Err50005, "เข้าสู่ระบบไม่สำเร็จ", "Staff is already registered with a LINE token")
	}

	// Update staff with the new line_token
	if err := tx.Table("staff").Where("id = ?", staff.ID).Update("line_token", request.LineToken).Error; err != nil {
		l.Errorf("update staff line_token error: %v", err)
		return nil, errs.NewInternalError()
	}

	staff.LineToken = &request.LineToken

	return responses.NewServiceSuccessResponse(staff), nil

}

func (a *adaptorPG) GetCustomerByPhone(request *GetCustomerByPhoneRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error) {
	l = logs.NewSpanLog(l)
	phone := request.Phone

	tx := a.db.Session(&gorm.Session{Logger: gorms.NewGormLog(l)})

	var customer domain.Customer
	if err := tx.Table("customer").Where("phone = ?", phone).Take(&customer).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// TODO implement send flex message to line for notice customer to register
			return nil, errs.NewCustom(404, errs.Err50004, "Customer not found", "Customer not found")
		}
		l.Errorf("query customer by phone error: %v", err)
		return nil, errs.NewInternalError()
	}

	return responses.NewServiceSuccessResponse(customer), nil
}
