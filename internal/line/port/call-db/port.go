package calldb

import (
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/responses"
)

type Port interface {
	GetStaffInfo(request *GetStaffInfoRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error)
	RegisterStaff(request *RegisterStaffRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error)
	GetCustomerByPhone(request *GetCustomerByPhoneRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error)
}

// GetStaffInfo request type
type GetStaffInfoRequest struct {
	LineToken string `validate:"required"`
}

// RegisterStaff request type
type RegisterStaffRequest struct {
	EmployeeID string `validate:"required"`
	Phone      string `validate:"required"`
	LineToken  string `validate:"required"`
}

// GetCustomerByPhone request type
type GetCustomerByPhoneRequest struct {
	Phone string `validate:"required"`
}
