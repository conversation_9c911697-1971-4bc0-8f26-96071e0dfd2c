package handler

import (
	"digital-transformation-api/infrastructure"
	calldb "digital-transformation-api/internal/line/port/call-db"
	"digital-transformation-api/internal/line/service/customer"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/gins"
	"digital-transformation-api/libs/logger"
	"net/http"

	"github.com/gin-gonic/gin"
)

type CustomerHandler struct {
	service customer.Service
}

func NewCustomerHandler(service customer.Service) *CustomerHandler {
	return &CustomerHandler{service: service}
}

func (h *CustomerHandler) HandleGetInfo(ctx *gin.Context, rctx *contexts.RouteContext, l logger.Logger) {
	// Validate line-token header
	if rctx.Header.LineToken == "" {
		_ = ctx.Error(errs.NewCustom(400, errs.Err40001, "Missing line-token in header", "Missing line-token in header"))
		ctx.Abort()
		return
	}

	// Get phone query parameter
	phone := ctx.Query("phone")
	if phone == "" {
		_ = ctx.Error(errs.NewCustom(400, errs.Err40001, "Missing phone query parameter", "Phone query parameter is required"))
		ctx.Abort()
		return
	}

	// Call service
	resp, err := h.service.GetCustomerByPhone(&customer.GetCustomerByPhoneRequest{Phone: phone}, rctx, l)
	if err != nil {
		_ = ctx.Error(err)
		ctx.Abort()
		return
	}

	ctx.JSON(http.StatusOK, resp)
}

func BindCustomerRoute(app gins.GinApps) {
	svc := customer.New(
		calldb.NewAdaptorPG(infrastructure.Db),
	)
	hdl := NewCustomerHandler(svc)
	app.Register(
		http.MethodGet,
		"/line/customer/get-info",
		app.ParseRouteContext(hdl.HandleGetInfo),
	)
}
