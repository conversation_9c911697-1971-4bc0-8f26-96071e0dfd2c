package customer

import (
	"digital-transformation-api/infrastructure"
	"digital-transformation-api/internal/common/domain"
	calldb "digital-transformation-api/internal/line/port/call-db"
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/responses"
)

type service struct {
	callDb calldb.Port
}

func New(callDb calldb.Port) Service {
	return &service{
		callDb: callDb,
	}
}

func (s *service) GetCustomerByPhone(request *GetCustomerByPhoneRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error) {
	if rctx == nil || rctx.Header.LineToken == "" {
		return nil, errs.NewBadRequestError()
	}

	if err := infrastructure.Validate.Struct(request); err != nil {
		l.Errorf("failed when validate request: %v", err)
		return nil, errs.NewBadRequestError()
	}

	dbResp, err := s.callDb.GetCustomerByPhone(&calldb.GetCustomerByPhoneRequest{Phone: request.Phone}, rctx, l)
	if err != nil {
		return nil, err
	}
	if dbResp == nil {
		return responses.NewServiceSuccessResponse(domain.Customer{}), nil
	}

	return dbResp, nil
}
