package customer

import (
	"digital-transformation-api/libs/contexts"
	"digital-transformation-api/libs/errs"
	"digital-transformation-api/libs/logger"
	"digital-transformation-api/libs/responses"
)

type Service interface {
	GetCustomerByPhone(request *GetCustomerByPhoneRequest, rctx *contexts.RouteContext, l logger.Logger) (*responses.ServiceResponse, errs.Error)
}

type GetCustomerByPhoneRequest struct {
	Phone string `validate:"required"`
}
